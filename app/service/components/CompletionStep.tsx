import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Image, TextInput, Switch, Alert } from 'react-native';
import { Camera, Upload, AlertTriangle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ServiceData } from '../hooks/useServiceFlow';
import { usePhotoAnalysis } from '../hooks/usePhotoAnalysis';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText } from '@/utils/parseFormula';

interface CompletionStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
}

export const CompletionStep: React.FC<CompletionStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack,
  onSave
}) => {
  const [consumeInventory, setConsumeInventory] = useState(false);
  const [isConsumingInventory, setIsConsumingInventory] = useState(false);

  const { takePhoto, pickImage } = usePhotoAnalysis();

  const handleResultImageCapture = (uri: string) => {
    onUpdate({ resultImage: uri });
    onSave?.();
  };

  const handleSatisfactionChange = (rating: number) => {
    onUpdate({ clientSatisfaction: rating });
    onSave?.();
  };

  const handleNotesChange = (notes: string) => {
    onUpdate({ resultNotes: notes });
    onSave?.();
  };

  const handleInventoryConsumption = async () => {
    if (!data.formula || !consumeInventory) return;

    setIsConsumingInventory(true);
    
    try {
      const colorFormula = parseFormulaText(data.formula);
      await InventoryConsumptionService.consumeProducts(colorFormula);
      
      Alert.alert(
        "Inventario actualizado",
        "Los productos han sido descontados del inventario correctamente.",
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error('Error consuming inventory:', error);
      Alert.alert(
        "Error",
        "No se pudo actualizar el inventario. Por favor, hazlo manualmente.",
        [{ text: "OK" }]
      );
    } finally {
      setIsConsumingInventory(false);
    }
  };

  const handleFinish = async () => {
    // Consume inventory if enabled
    if (consumeInventory && data.formula) {
      await handleInventoryConsumption();
    }
    
    // Proceed to finish
    onNext();
  };

  const salonConfig = useSalonConfigStore.getState();
  const showInventoryControl = data.formula && salonConfig.configuration.inventoryControlLevel === 'control-total';

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Resultado Final</Text>
        {data.client && (
          <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
        )}

        <Text style={styles.sectionTitle}>Fotos del resultado final</Text>
        <View style={styles.photoContainer}>
          {data.resultImage ? (
            <Image source={{ uri: data.resultImage }} style={styles.photoPreview} />
          ) : (
            <View style={styles.photoPlaceholder}>
              <Camera size={40} color={Colors.light.gray} />
              <Text style={styles.photoPlaceholderText}>Fotografía del resultado</Text>
            </View>
          )}
          <View style={styles.photoButtons}>
            <TouchableOpacity 
              style={styles.photoButton}
              onPress={() => takePhoto(handleResultImageCapture)}
            >
              <Camera size={16} color="white" />
              <Text style={styles.photoButtonText}>Usar Cámara</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.photoButton}
              onPress={() => pickImage(handleResultImageCapture)}
            >
              <Upload size={16} color="white" />
              <Text style={styles.photoButtonText}>Seleccionar</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.photoTip}>
            🔒 PRIVACIDAD: Las imágenes se procesan con difuminado facial automático y se eliminan inmediatamente después del análisis.
          </Text>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Satisfacción del cliente (1-5)</Text>
          <View style={styles.satisfactionContainer}>
            {[1, 2, 3, 4, 5].map((rating) => (
              <TouchableOpacity
                key={rating}
                style={[
                  styles.satisfactionButton,
                  data.clientSatisfaction === rating && styles.satisfactionButtonActive
                ]}
                onPress={() => handleSatisfactionChange(rating)}
              >
                <Text style={[
                  styles.satisfactionButtonText,
                  data.clientSatisfaction === rating && styles.satisfactionButtonTextActive
                ]}>
                  {rating}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Notas finales</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={data.resultNotes}
            onChangeText={handleNotesChange}
            placeholder="Observaciones sobre el resultado, ajustes realizados, recomendaciones para el cliente, etc."
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
        
        {/* Inventory Consumption Section */}
        {showInventoryControl && (
          <View style={styles.inventoryConsumptionSection}>
            <View style={styles.inventoryConsumptionHeader}>
              <Text style={styles.inventoryConsumptionTitle}>Control de Inventario</Text>
              <Switch
                trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
                thumbColor="white"
                ios_backgroundColor={Colors.light.lightGray}
                onValueChange={setConsumeInventory}
                value={consumeInventory}
              />
            </View>
            
            {consumeInventory && data.formulaCost && (
              <View style={styles.inventoryConsumptionDetails}>
                <Text style={styles.inventoryConsumptionSubtitle}>Productos a descontar:</Text>
                {data.formulaCost.items.map((item, index) => (
                  <View key={index} style={styles.inventoryItem}>
                    <Text style={styles.inventoryItemName}>{item.product}</Text>
                    <Text style={styles.inventoryItemAmount}>{item.amount}</Text>
                  </View>
                ))}
                
                <View style={styles.inventoryWarning}>
                  <AlertTriangle size={16} color={Colors.light.warning} />
                  <Text style={styles.inventoryWarningText}>
                    Esta acción descontará estos productos de tu inventario
                  </Text>
                </View>
              </View>
            )}
          </View>
        )}

        {/* Finish Button */}
        <TouchableOpacity 
          style={[styles.finishButton, isConsumingInventory && styles.finishButtonDisabled]}
          onPress={handleFinish}
          disabled={isConsumingInventory}
        >
          <Text style={styles.finishButtonText}>
            {isConsumingInventory ? "Guardando servicio..." : "Finalizar Servicio"}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  photoContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  photoPreview: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    marginBottom: 15,
  },
  photoPlaceholder: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    backgroundColor: "#F0F0F5",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 15,
  },
  photoPlaceholderText: {
    marginTop: 10,
    color: Colors.light.gray,
  },
  photoButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  photoButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 15,
    flex: 1,
    marginHorizontal: 5,
    justifyContent: "center",
  },
  photoButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 5,
  },
  photoTip: {
    fontSize: 12,
    color: Colors.light.success,
    marginTop: 10,
    textAlign: "center",
    fontWeight: "500",
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: "500",
    marginBottom: 8,
  },
  satisfactionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  satisfactionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.light.border,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  satisfactionButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  satisfactionButtonText: {
    fontSize: 18,
    fontWeight: "bold",
  },
  satisfactionButtonTextActive: {
    color: "white",
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: "top",
  },
  inventoryConsumptionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  inventoryConsumptionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  inventoryConsumptionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  inventoryConsumptionDetails: {
    marginTop: 12,
  },
  inventoryConsumptionSubtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 12,
  },
  inventoryItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + "30",
  },
  inventoryItemName: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
  },
  inventoryItemAmount: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.gray,
  },
  inventoryWarning: {
    backgroundColor: Colors.light.warning + "10",
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  inventoryWarningText: {
    fontSize: 12,
    color: Colors.light.warning,
    flex: 1,
  },
  finishButton: {
    backgroundColor: Colors.light.success,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 20,
    shadowColor: Colors.light.success,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  finishButtonDisabled: {
    backgroundColor: Colors.light.gray,
    shadowOpacity: 0,
    elevation: 0,
  },
  finishButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
});
