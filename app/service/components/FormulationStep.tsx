import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Switch, TextInput, ActivityIndicator, Alert } from 'react-native';
import { ChevronRight, Zap, Package, CheckCircle, AlertTriangle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ServiceData } from '../hooks/useServiceFlow';
import { useFormulation } from '../hooks/useFormulation';
import { useSalonConfigStore } from '@/stores/salon-config-store';

// Import existing components
import ViabilityIndicator from '@/components/ViabilityIndicator';
import FormulaCostBreakdown from '@/components/FormulaCostBreakdown';
import { BrandSelectionModal } from '@/components/BrandSelectionModal';
import FormulaDisplay from '@/components/formulation/FormulaDisplay';

interface FormulationStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
  analysisResult?: any; // AI analysis result from diagnosis
}

export const FormulationStep: React.FC<FormulationStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack,
  onSave,
  analysisResult
}) => {
  const {
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,
    formulaCost,
    viabilityAnalysis,
    stockValidation,
    checkStockAvailability,
    generateFormulaWithAI,
    analyzeViability
  } = useFormulation();

  // Initialize state from parent data only once
  React.useEffect(() => {
    if (!selectedBrand && data.selectedBrand) {
      setSelectedBrand(data.selectedBrand);
    }
    if (!selectedLine && data.selectedLine) {
      setSelectedLine(data.selectedLine);
    }
    if (!formula && data.formula) {
      setFormula(data.formula);
      setIsFormulaFromAI(data.isFormulaFromAI);
    }
  }, []); // Empty dependency array - only run once

  // Update parent when specific state changes (avoiding infinite loops)
  React.useEffect(() => {
    if (formula || formulaCost || viabilityAnalysis || stockValidation) {
      onUpdate({
        selectedBrand,
        selectedLine,
        formula,
        isFormulaFromAI,
        formulaCost,
        viabilityAnalysis,
        stockValidation
      });
    }
  }, [formula, formulaCost, viabilityAnalysis, stockValidation]); // Removed onUpdate from dependencies

  // Analyze viability when desired analysis changes
  React.useEffect(() => {
    if (data.desiredAnalysisResult && analysisResult) {
      const viability = analyzeViability(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis
      );
      onUpdate({ viabilityAnalysis: viability });
    }
  }, [data.desiredAnalysisResult, analysisResult, data.zoneColorAnalysis]);

  const handleGenerateFormula = async () => {
    try {
      const message = await generateFormulaWithAI(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis,
        data.clientId || undefined
      );
      
      onSave?.();
      
      if (message) {
        console.log(message);
      }
    } catch (error) {
      console.error('Error generating formula:', error);
    }
  };

  const handleBrandSelection = (brand: string, line: string) => {
    if (brandModalType === 'main') {
      setSelectedBrand(brand);
      setSelectedLine(line);
      onSave?.();
      
      // If in conversion mode and target brand equals source brand, clear source
      if (conversionMode && brand === originalBrand) {
        setOriginalBrand("");
        setOriginalLine("");
        Alert.alert("Atención", "La marca de destino no puede ser igual a la marca de origen");
      }
    } else {
      // Conversion mode - setting original brand
      if (brand === selectedBrand) {
        Alert.alert("Atención", "La marca de origen debe ser diferente a tu marca destino");
        return;
      }
      setOriginalBrand(brand);
      setOriginalLine(line);
    }
    setShowBrandModal(false);
  };

  const handleStockCheck = async () => {
    try {
      await checkStockAvailability();
    } catch (error) {
      console.error('Error checking stock:', error);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Formulación Ultra-Inteligente</Text>
        {data.client && (
          <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
        )}
        
        {/* Color Transition Summary */}
        {analysisResult && data.desiredAnalysisResult && (
          <View style={styles.colorTransitionCard}>
            <View style={styles.colorTransitionHeader}>
              <Text style={styles.colorTransitionTitle}>Transformación de Color</Text>
            </View>
            <View style={styles.colorTransitionContent}>
              <View style={styles.colorBox}>
                <View style={[styles.colorSample, { backgroundColor: '#8B4513' }]} />
                <Text style={styles.colorLevel}>Nivel {analysisResult.averageDepthLevel || 5}</Text>
                <Text style={styles.colorTone}>{analysisResult.overallTone || 'Castaño'}</Text>
              </View>
              <ChevronRight size={24} color={Colors.light.primary} style={styles.arrowIcon} />
              <View style={styles.colorBox}>
                <View style={[styles.colorSample, { backgroundColor: '#F5DEB3' }]} />
                <Text style={styles.colorLevel}>Nivel {data.desiredAnalysisResult.general.overallLevel || 8}</Text>
                <Text style={styles.colorTone}>{data.desiredAnalysisResult.general.overallTone || 'Rubio'}</Text>
              </View>
            </View>
            {data.desiredAnalysisResult.general.technique && (
              <View style={styles.techniqueIndicator}>
                <Text style={styles.techniqueLabel}>Técnica: {data.desiredAnalysisResult.general.technique}</Text>
              </View>
            )}
          </View>
        )}

        <View style={styles.formGroup}>
          <Text style={styles.label}>Marca</Text>
          <TouchableOpacity 
            style={styles.selectContainer}
            onPress={() => {
              setBrandModalType('main');
              setShowBrandModal(true);
            }}
          >
            <Text style={styles.selectText}>{selectedBrand}</Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </TouchableOpacity>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Línea</Text>
          <TouchableOpacity 
            style={styles.selectContainer}
            onPress={() => {
              setBrandModalType('main');
              setShowBrandModal(true);
            }}
          >
            <Text style={styles.selectText}>{selectedLine}</Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </TouchableOpacity>
        </View>
        
        {/* Brand Conversion Section */}
        <View style={[styles.conversionSection, conversionMode && styles.conversionSectionActive]}>
          <TouchableOpacity 
            style={styles.conversionHeader}
            activeOpacity={0.7}
            onPress={() => setConversionMode(!conversionMode)}
          >
            <View style={styles.conversionTitleContainer}>
              <Text style={styles.conversionTitle}>Adaptar fórmula de otra marca</Text>
              <Text style={styles.conversionSubtitle}>Obtén el equivalente en tu marca</Text>
            </View>
            <Switch
              trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
              thumbColor={conversionMode ? Colors.light.primary : Colors.light.gray}
              ios_backgroundColor={Colors.light.lightGray}
              onValueChange={setConversionMode}
              value={conversionMode}
              style={styles.conversionSwitch}
            />
          </TouchableOpacity>
          
          {conversionMode && (
            <View style={styles.conversionContent}>
              {/* Formula Input First */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>¿Cuál es la fórmula?</Text>
                <TextInput
                  style={[styles.input, styles.textArea, styles.conversionFormulaInput]}
                  value={originalFormula}
                  onChangeText={setOriginalFormula}
                  placeholder="Ej: 7.31 + 8.34 (2:1) • Oxidante 20 vol • 35 min"
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>

              <TouchableOpacity 
                style={styles.conversionBrandContainer}
                onPress={() => {
                  setBrandModalType('conversion');
                  setShowBrandModal(true);
                }}
              >
                <Text style={styles.conversionLabel}>¿De qué marca es la fórmula?</Text>
                <View style={styles.conversionSelectContainer}>
                  <Text style={styles.selectText}>
                    {originalBrand || "Seleccionar marca..."}
                  </Text>
                  <ChevronRight size={16} color={Colors.light.gray} />
                </View>
              </TouchableOpacity>
              
              {originalBrand && (
                <TouchableOpacity 
                  style={styles.conversionBrandContainer}
                  onPress={() => {
                    setBrandModalType('conversion');
                    setShowBrandModal(true);
                  }}
                >
                  <Text style={styles.conversionLabel}>¿Qué línea o producto?</Text>
                  <View style={styles.conversionSelectContainer}>
                    <Text style={styles.selectText}>
                      {originalLine || "Seleccionar línea..."}
                    </Text>
                    <ChevronRight size={16} color={Colors.light.gray} />
                  </View>
                </TouchableOpacity>
              )}
              
              {originalBrand && originalLine && (
                <View style={styles.conversionInfoBox}>
                  <Text style={styles.conversionInfoText}>
                    ✨ Se adaptará a {selectedBrand} {selectedLine}
                  </Text>
                  <Text style={styles.conversionInfoSubtext}>
                    Manteniendo el mismo resultado de color
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>

        <TouchableOpacity 
          style={[styles.aiButton, isGeneratingFormula && styles.aiButtonDisabled]}
          onPress={handleGenerateFormula}
          disabled={isGeneratingFormula}
        >
          {isGeneratingFormula ? (
            <View style={styles.aiButtonLoading}>
              <ActivityIndicator size="small" color="white" />
              <Text style={styles.aiButtonText}>Generando fórmula...</Text>
            </View>
          ) : (
            <View style={styles.aiButtonContent}>
              <Zap size={20} color="white" />
              <Text style={styles.aiButtonText}>
                {conversionMode && originalBrand && originalLine ? 'Convertir y Generar Fórmula' : 'Generar Fórmula Ultra-Inteligente'}
              </Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Formula Display Section */}
        {formula && (
          <FormulaDisplay
            formulaText={formula}
            clientName={data.client?.name}
            serviceDate={new Date().toLocaleDateString()}
            isFromAI={isFormulaFromAI}
            onEdit={setFormula}
            editable={true}
            viabilityAnalysis={viabilityAnalysis}
            currentLevel={parseInt(data.overallTone.split('/')[0]) || 5}
            targetLevel={parseInt(data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || "7") || 7}
          />
        )}

        {/* Viability Analysis */}
        {viabilityAnalysis && (
          <ViabilityIndicator analysis={viabilityAnalysis} />
        )}

        {/* Formula Cost Breakdown */}
        {formulaCost && (
          <FormulaCostBreakdown 
            cost={formulaCost} 
            isRealCost={useSalonConfigStore.getState().configuration.inventoryControlLevel !== 'solo-formulas'}
          />
        )}
        
        {/* Stock Validation */}
        {formula && useSalonConfigStore.getState().configuration.inventoryControlLevel === 'control-total' && (
          <View style={styles.stockValidationSection}>
            {/* Show validation status automatically after formula generation */}
            {stockValidation.checked && !stockValidation.hasStock && (
              <View style={[styles.stockWarningBanner, styles.stockStatusError]}>
                <AlertTriangle size={24} color={Colors.light.error} />
                <View style={styles.stockWarningContent}>
                  <Text style={styles.stockWarningTitle}>⚠️ Stock insuficiente</Text>
                  <Text style={styles.stockWarningSubtitle}>Faltan estos productos:</Text>
                  {stockValidation.missingProducts.map((product, index) => (
                    <Text key={index} style={styles.missingProductText}>• {product}</Text>
                  ))}
                </View>
              </View>
            )}
            
            <TouchableOpacity 
              style={[styles.stockCheckButton, stockValidation.isChecking && styles.stockCheckButtonDisabled]}
              onPress={handleStockCheck}
              disabled={stockValidation.isChecking}
            >
              {stockValidation.isChecking ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <>
                  <Package size={20} color="white" />
                  <Text style={styles.stockCheckButtonText}>
                    {stockValidation.checked ? 'Verificar Stock Nuevamente' : 'Verificar Disponibilidad'}
                  </Text>
                </>
              )}
            </TouchableOpacity>
            
            {/* Show success message only if stock is available */}
            {stockValidation.checked && stockValidation.hasStock && (
              <View style={[styles.stockStatus, styles.stockStatusSuccess]}>
                <CheckCircle size={20} color={Colors.light.success} />
                <Text style={styles.stockStatusTextSuccess}>✅ Stock disponible para todos los productos</Text>
              </View>
            )}
          </View>
        )}

        {/* Continue Button */}
        {formula && (
          <TouchableOpacity
            style={styles.continueButton}
            onPress={onNext}
          >
            <Text style={styles.continueButtonText}>Continuar al Resultado Final</Text>
          </TouchableOpacity>
        )}

        {/* Brand Selection Modal */}
        <BrandSelectionModal
          visible={showBrandModal}
          onClose={() => setShowBrandModal(false)}
          onSelectBrand={handleBrandSelection}
          currentBrand={brandModalType === 'main' ? selectedBrand : originalBrand}
          currentLine={brandModalType === 'main' ? selectedLine : originalLine}
          title={brandModalType === 'main' ? "Seleccionar Marca y Línea" : "Marca y Línea Original"}
          isConversionMode={brandModalType === 'conversion'}
          sourceBrand={brandModalType === 'conversion' ? originalBrand : undefined}
          sourceLine={brandModalType === 'conversion' ? originalLine : undefined}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  colorTransitionCard: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  colorTransitionHeader: {
    marginBottom: 16,
  },
  colorTransitionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
  },
  colorTransitionContent: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
  },
  colorBox: {
    alignItems: "center",
  },
  colorSample: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 8,
    borderWidth: 3,
    borderColor: Colors.light.border,
  },
  colorLevel: {
    fontSize: 16,
    fontWeight: "700",
    color: Colors.light.text,
  },
  colorTone: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
  },
  arrowIcon: {
    marginHorizontal: 20,
  },
  techniqueIndicator: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  techniqueLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.primary,
    textAlign: "center",
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: "500",
    marginBottom: 8,
  },
  selectContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  selectText: {
    fontSize: 15,
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: "top",
  },
  conversionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  conversionSectionActive: {
    borderColor: Colors.light.primary,
    borderWidth: 2,
  },
  conversionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    minHeight: 60,
  },
  conversionTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  conversionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  conversionSubtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  conversionSwitch: {
    transform: [{ scale: 0.8 }],
  },
  conversionContent: {
    marginTop: 8,
  },
  conversionBrandContainer: {
    marginBottom: 16,
  },
  conversionLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.text,
    marginBottom: 8,
  },
  conversionSelectContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  conversionFormulaInput: {
    minHeight: 140,
    paddingTop: 12,
  },
  conversionInfoBox: {
    backgroundColor: Colors.light.primary + "10",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary + "20",
  },
  conversionInfoText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.primary,
    marginBottom: 4,
  },
  conversionInfoSubtext: {
    fontSize: 13,
    color: Colors.light.text,
    lineHeight: 18,
  },
  aiButton: {
    backgroundColor: Colors.light.accent,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginBottom: 20,
    shadowColor: Colors.light.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  aiButtonDisabled: {
    backgroundColor: Colors.light.gray,
    shadowOpacity: 0,
    elevation: 0,
  },
  aiButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  aiButtonLoading: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  aiButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  stockValidationSection: {
    backgroundColor: Colors.light.primary + "05",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.primary + "20",
  },
  stockWarningBanner: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: Colors.light.error + "10",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.error + "30",
    gap: 12,
  },
  stockWarningContent: {
    flex: 1,
  },
  stockWarningTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.error,
    marginBottom: 4,
  },
  stockWarningSubtitle: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 8,
  },
  missingProductText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 4,
    marginLeft: 24,
  },
  stockCheckButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    gap: 8,
  },
  stockCheckButtonDisabled: {
    backgroundColor: Colors.light.gray,
    opacity: 0.6,
  },
  stockCheckButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 15,
  },
  stockStatus: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  stockStatusSuccess: {
    backgroundColor: Colors.light.success + "10",
    borderWidth: 1,
    borderColor: Colors.light.success + "30",
  },
  stockStatusError: {
    backgroundColor: Colors.light.error + "10",
    borderWidth: 1,
    borderColor: Colors.light.error + "30",
  },
  stockStatusTextSuccess: {
    color: Colors.light.success,
    fontSize: 14,
    fontWeight: "500",
    flex: 1,
  },
  continueButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 20,
    marginBottom: 10,
  },
  continueButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});
