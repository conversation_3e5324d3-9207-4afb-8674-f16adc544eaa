import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';

interface Step {
  id: string;
  title: string;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
  onStepPress?: (stepIndex: number) => void;
  canNavigateToStep?: (stepIndex: number) => boolean;
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStep,
  onStepPress,
  canNavigateToStep
}) => {
  const handleStepPress = (stepIndex: number) => {
    if (onStepPress && canNavigateToStep && canNavigateToStep(stepIndex)) {
      onStepPress(stepIndex);
    }
  };

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'current';
    return 'upcoming';
  };

  const isStepClickable = (stepIndex: number) => {
    return canNavigateToStep ? canNavigateToStep(stepIndex) : stepIndex <= currentStep;
  };

  return (
    <View style={styles.container}>
      <View style={styles.progressContainer}>
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const isClickable = isStepClickable(index);
          
          return (
            <React.Fragment key={step.id}>
              <TouchableOpacity
                style={[
                  styles.stepCircle,
                  status === 'completed' && styles.stepCircleCompleted,
                  status === 'current' && styles.stepCircleCurrent,
                  status === 'upcoming' && styles.stepCircleUpcoming,
                  !isClickable && styles.stepCircleDisabled
                ]}
                onPress={() => handleStepPress(index)}
                disabled={!isClickable}
                activeOpacity={isClickable ? 0.7 : 1}
              >
                {status === 'completed' ? (
                  <CheckCircle size={16} color="white" />
                ) : (
                  <Text style={[
                    styles.stepNumber,
                    status === 'current' && styles.stepNumberCurrent,
                    status === 'upcoming' && styles.stepNumberUpcoming
                  ]}>
                    {index + 1}
                  </Text>
                )}
              </TouchableOpacity>
              
              {index < steps.length - 1 && (
                <View style={[
                  styles.progressLine,
                  index < currentStep && styles.progressLineCompleted
                ]} />
              )}
            </React.Fragment>
          );
        })}
      </View>
      
      <View style={styles.labelsContainer}>
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const isClickable = isStepClickable(index);
          
          return (
            <TouchableOpacity
              key={`label-${step.id}`}
              style={styles.stepLabel}
              onPress={() => handleStepPress(index)}
              disabled={!isClickable}
              activeOpacity={isClickable ? 0.7 : 1}
            >
              <Text style={[
                styles.stepLabelText,
                status === 'completed' && styles.stepLabelTextCompleted,
                status === 'current' && styles.stepLabelTextCurrent,
                status === 'upcoming' && styles.stepLabelTextUpcoming,
                !isClickable && styles.stepLabelTextDisabled
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingVertical: 20,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  stepCircleCompleted: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  stepCircleCurrent: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  stepCircleUpcoming: {
    backgroundColor: 'white',
    borderColor: Colors.light.lightGray,
  },
  stepCircleDisabled: {
    opacity: 0.5,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
  },
  stepNumberCurrent: {
    color: 'white',
  },
  stepNumberUpcoming: {
    color: Colors.light.gray,
  },
  progressLine: {
    height: 2,
    width: 40,
    backgroundColor: Colors.light.lightGray,
    marginHorizontal: 8,
  },
  progressLineCompleted: {
    backgroundColor: Colors.light.success,
  },
  labelsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stepLabel: {
    flex: 1,
    alignItems: 'center',
  },
  stepLabelText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 16,
  },
  stepLabelTextCompleted: {
    color: Colors.light.success,
  },
  stepLabelTextCurrent: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  stepLabelTextUpcoming: {
    color: Colors.light.gray,
  },
  stepLabelTextDisabled: {
    opacity: 0.5,
  },
});
