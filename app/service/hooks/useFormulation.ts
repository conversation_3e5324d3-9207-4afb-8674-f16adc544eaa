import { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useInventoryStore } from '@/stores/inventory-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { ColorCorrectionService } from '@/services/colorCorrectionService';
import { brandConversionService } from '@/services/brandConversionService';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText, calculateSimpleFormulaCost } from '@/utils/parseFormula';
import { ViabilityAnalysis, FormulaCost, ColorFormula } from '@/types/formulation';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { HairZone, ZoneColorAnalysis, UnwantedTone } from '@/types/hair-diagnosis';
import { COLOR_TECHNIQUES } from '@/types/desired-photo';

export const useFormulation = () => {
  const [selectedBrand, setSelectedBrand] = useState("Wella Professionals");
  const [selectedLine, setSelectedLine] = useState("Illumina Color");
  const [formula, setFormula] = useState("");
  const [isFormulaFromAI, setIsFormulaFromAI] = useState(true);
  const [isGeneratingFormula, setIsGeneratingFormula] = useState(false);
  
  // Brand conversion states
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [brandModalType, setBrandModalType] = useState<'main' | 'conversion'>('main');
  const [conversionMode, setConversionMode] = useState(false);
  const [originalBrand, setOriginalBrand] = useState("");
  const [originalLine, setOriginalLine] = useState("");
  const [originalFormula, setOriginalFormula] = useState("");
  
  const [formulaCost, setFormulaCost] = useState<FormulaCost | null>(null);
  const [viabilityAnalysis, setViabilityAnalysis] = useState<ViabilityAnalysis | null>(null);
  const [stockValidation, setStockValidation] = useState<{
    isChecking: boolean;
    hasStock: boolean;
    missingProducts: string[];
    checked: boolean;
  }>({
    isChecking: false,
    hasStock: true,
    missingProducts: [],
    checked: false,
  });

  // Client History Store
  const {
    getCompatibleFormulas,
    getRecommendationsForClient,
  } = useClientHistoryStore();

  const calculateFormulaCost = useCallback(async (formulaText: string): Promise<FormulaCost> => {
    try {
      const colorFormula = parseFormulaText(formulaText);
      const salonConfig = useSalonConfigStore.getState();
      
      if (salonConfig.configuration.inventoryControlLevel === 'solo-formulas') {
        // Use simple calculation for formula-only mode
        return calculateSimpleFormulaCost(formulaText);
      } else {
        // Use inventory-based calculation
        const consumptionAnalysis = await InventoryConsumptionService.calculateFormulationCost(colorFormula);

        // Convert to FormulaCost format
        const items: FormulaCost['items'] = consumptionAnalysis.items.map(item => ({
          product: item.productName,
          amount: `${item.amount}${item.unit}`,
          unitCost: item.unitCost,
          totalCost: item.totalCost
        }));

        const totalMaterialCost = consumptionAnalysis.totalCost;

        // Apply salon's configured markup
        const salonConfig = useSalonConfigStore.getState();
        const suggestedServicePrice = salonConfig.applyMarkup(totalMaterialCost);
        const profitMargin = suggestedServicePrice - totalMaterialCost;

        return {
          items,
          totalMaterialCost: Math.round(totalMaterialCost * 100) / 100,
          suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
          profitMargin: Math.round(profitMargin * 100) / 100
        };
      }
    } catch (error) {
      console.error('Error calculating formula cost:', error);
      // Fallback to simple calculation
      return calculateSimpleFormulaCost(formulaText);
    }
  }, []);

  const analyzeViability = useCallback((
    analysisResult: any,
    desiredAnalysisResult: DesiredColorAnalysisResult | null,
    zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
  ): ViabilityAnalysis => {
    if (!analysisResult || !desiredAnalysisResult) {
      return {
        overall: 'medium',
        factors: [],
        recommendations: [],
        warnings: [],
        timeEstimate: '2-3 horas',
        sessionsNeeded: 1,
        riskLevel: 'medium'
      };
    }

    const factors = [];
    const recommendations = [];
    const warnings = [];
    let overall: 'high' | 'medium' | 'low' = 'medium';
    let riskLevel: 'low' | 'medium' | 'high' = 'medium';
    let sessionsNeeded = 1;

    // Analyze level difference
    const currentLevel = analysisResult.averageDepthLevel || 5;
    const targetLevel = parseInt(desiredAnalysisResult.general.overallLevel?.split('/')[0] || "7") || 7;
    const levelDifference = Math.abs(targetLevel - currentLevel);

    if (levelDifference > 3) {
      factors.push('Cambio de nivel significativo');
      warnings.push('Cambio de más de 3 niveles puede requerir múltiples sesiones');
      riskLevel = 'high';
      sessionsNeeded = Math.ceil(levelDifference / 3);
    } else if (levelDifference > 1) {
      factors.push('Cambio de nivel moderado');
      overall = 'medium';
    } else {
      factors.push('Cambio de nivel mínimo');
      overall = 'high';
    }

    // Analyze hair condition
    const rootsPhysical = zoneColorAnalysis[HairZone.ROOTS];
    if (rootsPhysical?.damage === 'Alto' || rootsPhysical?.damage === 'Severo') {
      warnings.push('Cabello con daño severo - considerar tratamiento previo');
      riskLevel = 'high';
      recommendations.push('Aplicar tratamiento reconstructor antes del color');
    }

    // Analyze porosity
    if (rootsPhysical?.porosity === 'Alta') {
      factors.push('Porosidad alta detectada');
      recommendations.push('Usar productos de baja alcalinidad');
      recommendations.push('Reducir tiempo de procesamiento');
    }

    // Time estimation
    let timeEstimate = '1-2 horas';
    if (levelDifference > 2) timeEstimate = '2-3 horas';
    if (levelDifference > 4) timeEstimate = '3-4 horas';
    if (sessionsNeeded > 1) timeEstimate = `${sessionsNeeded} sesiones de 2-3 horas cada una`;

    return {
      overall,
      factors,
      recommendations,
      warnings,
      timeEstimate,
      sessionsNeeded,
      riskLevel
    };
  }, []);

  const checkStockAvailability = useCallback(async () => {
    if (!formula) return;

    setStockValidation(prev => ({ ...prev, isChecking: true }));

    try {
      const colorFormula = parseFormulaText(formula);
      const stockCheck = await InventoryConsumptionService.checkStock(colorFormula);
      
      setStockValidation({
        isChecking: false,
        hasStock: stockCheck.hasStock,
        missingProducts: stockCheck.missingProducts,
        checked: true,
      });

      return stockCheck;
    } catch (error) {
      console.error('Error checking stock:', error);
      setStockValidation(prev => ({ 
        ...prev, 
        isChecking: false,
        checked: true,
        hasStock: false,
        missingProducts: ['Error al verificar stock']
      }));
    }
  }, [formula]);

  const generateFormulaWithAI = useCallback(async (
    analysisResult: any,
    desiredAnalysisResult: DesiredColorAnalysisResult | null,
    zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
    clientId?: string
  ) => {
    if (!analysisResult && !desiredAnalysisResult) {
      Alert.alert("Error", "Necesitas completar el diagnóstico y el análisis del color deseado antes de generar la fórmula");
      return;
    }

    setIsGeneratingFormula(true);
    
    try {
      // Get client history for better formula generation
      let historyContext = "";
      if (clientId) {
        const compatibleFormulas = getCompatibleFormulas(clientId);
        const recommendations = getRecommendationsForClient(clientId);
        
        if (compatibleFormulas.length > 0) {
          historyContext += `\nFórmulas exitosas anteriores:\n${compatibleFormulas.slice(0, 2).map(f => `- ${f.formula} (Satisfacción: ${f.satisfaction}/5)`).join('\n')}`;
        }
        
        if (recommendations.length > 0) {
          historyContext += `\nRecomendaciones basadas en historial:\n${recommendations.slice(0, 3).map(r => `- ${r}`).join('\n')}`;
        }
      }

      // Analyze color correction needs
      const unwantedTones: Partial<Record<HairZone, UnwantedTone>> = {};
      Object.values(zoneColorAnalysis).forEach(zone => {
        if (zone.unwantedTone && zone.zone) {
          unwantedTones[zone.zone] = zone.unwantedTone;
        }
      });
      
      const correctionAnalysis = ColorCorrectionService.analyzeColorCorrection(
        zoneColorAnalysis as Record<HairZone, ZoneColorAnalysis>,
        desiredAnalysisResult!,
        unwantedTones,
        selectedBrand,
        selectedLine
      );

      // Check if we should use the regional formulation service
      const salonConfig = useSalonConfigStore.getState();
      
      if (salonConfig.regionalConfig && analysisResult && desiredAnalysisResult) {
        const formulaContext = {
          currentDiagnosis: analysisResult,
          desiredResult: desiredAnalysisResult,
          brand: selectedBrand,
          line: selectedLine,
          regionalConfig: salonConfig.regionalConfig,
          clientHistory: historyContext,
          conversionMode: conversionMode ? {
            originalBrand: originalBrand || '',
            originalLine: originalLine || '',
            originalFormula: originalFormula || ''
          } : undefined
        };
        
        // Call Supabase Edge Function
        const { data, error } = await supabase.functions.invoke('salonier-assistant', {
          body: {
            task: 'generate_formula',
            payload: {
              diagnosis: formulaContext.currentDiagnosis,
              desiredResult: formulaContext.desiredResult,
              brand: formulaContext.brand,
              line: formulaContext.line,
              clientHistory: formulaContext.clientHistory,
              regionalConfig: salonConfig.regionalConfig
            }
          }
        });
        
        if (error) {
          throw new Error('Error al generar la fórmula con IA');
        }
        
        // Try multiple response structures
        let generatedFormula = null;
        
        if (data && data.success && data.data && data.data.formulaText) {
          generatedFormula = data.data.formulaText;
        } else if (data && data.formulaText) {
          generatedFormula = data.formulaText;
        } else if (data && data.result && data.result.formulaText) {
          generatedFormula = data.result.formulaText;
        } else if (data && typeof data === 'string') {
          generatedFormula = data;
        }
        
        if (!generatedFormula) {
          throw new Error('Respuesta inválida del servidor');
        }
        
        setFormula(generatedFormula);
        setIsFormulaFromAI(true);
        
        // Calculate formula cost
        const cost = await calculateFormulaCost(generatedFormula);
        setFormulaCost(cost);
        
        // Auto-validate stock if inventory control is enabled
        if (salonConfig.configuration.inventoryControlLevel === 'control-total') {
          const stockCheck = await checkStockAvailability();
          if (stockCheck && !stockCheck.hasStock) {
            return `⚠️ Stock insuficiente: ${stockCheck.missingProducts.join(', ')}`;
          }
        }
        
        return "✅ Fórmula generada con IA";
      } else {
        throw new Error('Configuración regional no disponible');
      }
    } catch (error) {
      console.error('Error in formula generation:', error);
      
      // Fallback to basic formula generation
      Alert.alert(
        '⚠️ Sin conexión con IA', 
        `Error al generar fórmula con IA.\n\nGenerando fórmula de ejemplo. Por favor ajusta manualmente según tu criterio profesional.\n\nPara usar IA verifica:\n• Conexión a internet\n• Configuración de OpenAI en Supabase`,
        [{ text: 'Entendido', style: 'default' }]
      );
      
      // Generate fallback formula
      const currentLevel = analysisResult?.averageDepthLevel || 5;
      const targetLevel = parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || "7") || 7;
      const levelDifference = Math.abs(targetLevel - currentLevel);
      const selectedTechnique = desiredAnalysisResult?.general?.technique || 'full_color';
      
      const fallbackFormula = `Fórmula Base:
- ${selectedLine} ${targetLevel}/1 (30g)
- ${selectedLine} ${targetLevel}/69 (10g)  
- Oxidante ${levelDifference > 2 ? '30' : '20'} vol (60g)

Aplicación estándar:
1. Dividir el cabello en secciones
2. Aplicar según técnica ${COLOR_TECHNIQUES.find(t => t.id === selectedTechnique)?.name || 'seleccionada'}
3. Procesar 35 minutos
4. Enjuagar y acondicionar`;

      setFormula(fallbackFormula);
      setIsFormulaFromAI(false);
      
      const cost = await calculateFormulaCost(fallbackFormula);
      setFormulaCost(cost);
      
      return "⚠️ Fórmula de ejemplo generada. Ajusta manualmente.";
    } finally {
      setIsGeneratingFormula(false);
    }
  }, [
    selectedBrand, 
    selectedLine, 
    conversionMode, 
    originalBrand, 
    originalLine, 
    originalFormula,
    getCompatibleFormulas,
    getRecommendationsForClient,
    calculateFormulaCost,
    checkStockAvailability
  ]);

  // Effect to recalculate cost when formula changes
  useEffect(() => {
    if (!formula || formula.trim() === '') {
      setFormulaCost(null);
      return;
    }

    const timeoutId = setTimeout(() => {
      calculateFormulaCost(formula).then(cost => {
        setFormulaCost(cost);
      }).catch(error => {
        console.error('Error calculating formula cost:', error);
        const simpleCost = calculateSimpleFormulaCost(formula);
        setFormulaCost(simpleCost);
      });
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [formula]);

  return {
    // Brand and formula state
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,
    
    // Brand conversion state
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,
    
    // Analysis and cost state
    formulaCost,
    setFormulaCost,
    viabilityAnalysis,
    setViabilityAnalysis,
    stockValidation,
    setStockValidation,
    
    // Functions
    calculateFormulaCost,
    analyzeViability,
    checkStockAvailability,
    generateFormulaWithAI
  };
};
